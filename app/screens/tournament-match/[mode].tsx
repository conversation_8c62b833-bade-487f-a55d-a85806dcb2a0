import React from "react";
import { useLocalSearchParams } from "expo-router";
import { TournamentMatchForm } from "@/utils/tournament-match-exports";

export default function TournamentMatchFormScreen() {
  const {
    "tournament-id": tournamentId,
    mode,
    "match-id": matchId,
  } = useLocalSearchParams();

  return (
    <TournamentMatchForm
      tournamentId={tournamentId as string}
      mode={mode as string}
      matchId={matchId as string}
    />
  );
}
