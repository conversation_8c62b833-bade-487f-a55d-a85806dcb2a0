import React, { useState, useCallback, memo } from "react";
import { Pressable, View, Text } from "react-native";
import { Match, CANCELLED } from "../../../types/matches";
import { Icon } from "@/components/ui/icon";
import {
  Edit2Icon,
  Trash2Icon,
  BanIcon,
  UserRoundPenIcon,
  UserXIcon,
  LucideIcon,
} from "lucide-react-native";
import SlideButton from "@/components/k-components/SlideButton";
import { triggerHapticFeedback } from "@/utils";
import { useRouter } from "expo-router";
import SCREENS from "@/constants/Screens";
import MatchDeleteConfirmationModal from "./MatchDeleteConfirmationModal";
import ActionMenuItem from "./ActionMenuItem";
import { useMatchDeletion } from "../hooks/useMatchDeletion";
import { updateMatch } from "@/services/matchService";
import { toast } from "@/toast/toast";
import {
  ACTION_COLORS,
  MATCH_ACTION_LABELS,
  COMPONENT_SIZES,
} from "@/constants/matchConstants";

interface MatchActionsProps {
  match: Match;
  onClose: () => void;
  onMatchDeleted?: () => void;
}

interface ActionItem {
  icon: LucideIcon;
  label: string;
  clickHandler: () => void;
  textColor?: string;
  disabled?: boolean;
}

interface HeaderAction {
  icon: LucideIcon;
  color: string;
  clickHandler: () => void;
}

const ActionIcon = ({
  onPress,
  icon,
  color,
}: {
  onPress: () => void;
  icon: LucideIcon;
  color: string;
}) => {
  const handlePress = () => {
    triggerHapticFeedback();
    onPress();
  };
  return (
    <Pressable
      onPress={handlePress}
      className={`p-2 border border-${color} rounded-full`}
    >
      <Icon as={icon} size="sm" className={`text-${color}`} />
    </Pressable>
  );
};

const MatchActions: React.FC<MatchActionsProps> = ({
  match,
  onClose,
  onMatchDeleted,
}) => {
  const router = useRouter();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [cancelling, setCancelling] = useState(false);
  const { deleting, handleDeleteMatch: deleteMatch } = useMatchDeletion();

  const handleStartMatch = useCallback(() => {
    console.log("Starting match:", match.id);
    // Feature not yet implemented
  }, [match.id]);

  const handleEditMatch = useCallback(() => {
    onClose();
    router.push({
      pathname: SCREENS.TOURNAMENT_MATCH_FORM,
      params: {
        "tournament-id": match.tournament_id,
        mode: "edit",
        "match-id": match.id,
      },
    });
  }, [onClose, router, match.tournament_id, match.id]);

  const handleDeleteMatch = useCallback(() => {
    setShowDeleteModal(true);
  }, []);

  const onDeleteConfirm = async () => {
    const result = await deleteMatch(match);
    if (result.success) {
      setShowDeleteModal(false);
      onClose();
      onMatchDeleted?.();
    } else {
      toast.error("Failed to delete match");
    }
  };

  const handleCancelMatch = async () => {
    setCancelling(true);
    try {
      const result = await updateMatch(match.id, { status: CANCELLED });

      if (result.success) {
        toast.success("Match cancelled successfully");
        onClose();
        onMatchDeleted?.();
      }
    } catch {
      toast.error("Failed to cancel match");
    } finally {
      setCancelling(false);
    }
  };

  const handleUpdateScore = () => {
    console.log("Updating score for match:", match.id);
    // Feature not yet implemented
  };

  const handleMarkAsWalkover = () => {
    console.log("Marking match as walkover:", match.id);
    // Feature not yet implemented
  };

  const headerActions: HeaderAction[] = [
    {
      icon: Edit2Icon,
      color: ACTION_COLORS.EDIT,
      clickHandler: handleEditMatch,
    },
    {
      icon: Trash2Icon,
      color: ACTION_COLORS.DELETE,
      clickHandler: handleDeleteMatch,
    },
  ];

  const menuActions: ActionItem[] = [
    {
      icon: BanIcon,
      label: cancelling
        ? MATCH_ACTION_LABELS.CANCELLING
        : MATCH_ACTION_LABELS.CANCEL,
      clickHandler: handleCancelMatch,
      textColor: ACTION_COLORS.CANCEL,
      disabled: cancelling,
    },
    {
      icon: UserRoundPenIcon,
      label: MATCH_ACTION_LABELS.UPDATE_SCORE,
      clickHandler: handleUpdateScore,
      textColor: ACTION_COLORS.UPDATE_SCORE,
    },
    {
      icon: UserXIcon,
      label: MATCH_ACTION_LABELS.WALKOVER,
      clickHandler: handleMarkAsWalkover,
      textColor: ACTION_COLORS.WALKOVER,
    },
  ];

  return (
    <View className="flex py-1">
      <View className="rounded-xl bg-white border border-gray-200 shadow-md pt-3">
        <View className="flex-row justify-between items-center mb-3 px-4">
          <Text className="text-sm font-urbanistExtraBold tracking-widest text-gray-500 self-center">
            {MATCH_ACTION_LABELS.MATCH_ACTIONS}
          </Text>
          <View className="flex-row justify-end gap-3">
            {headerActions.map((action, index) => (
              <ActionIcon
                key={index}
                onPress={action.clickHandler}
                icon={action.icon}
                color={action.color}
              />
            ))}
          </View>
        </View>
        <View className="border-t border-gray-300 border-dashed" />
        <View className="px-4">
          {menuActions.map((action, index) => (
            <ActionMenuItem
              key={index}
              icon={action.icon}
              label={action.label}
              onPress={action.clickHandler}
              textColor={action.textColor}
              disabled={action.disabled}
              showDivider={index < menuActions.length - 1}
            />
          ))}
        </View>
      </View>

      {/* Slide Button for Start Match */}
      <View className="mt-4">
        <SlideButton
          onSlideComplete={handleStartMatch}
          text={MATCH_ACTION_LABELS.START_MATCH}
          backgroundColor="bg-primary-0"
          textColor="text-white"
          width="auto"
          height={COMPONENT_SIZES.SLIDE_BUTTON_HEIGHT}
        />
      </View>

      {/* Delete Confirmation Modal */}
      <MatchDeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        match={match}
        onDeleteConfirm={onDeleteConfirm}
        loading={deleting}
      />
    </View>
  );
};

export default memo(MatchActions);
