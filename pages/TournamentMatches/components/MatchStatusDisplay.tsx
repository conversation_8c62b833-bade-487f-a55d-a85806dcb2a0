import React, { memo } from "react";
import { View } from "react-native";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { formatDate, formatTime } from "@/utils/dateTimeUtils";
import {
  getMatchStatusBasedOnDate,
  getStatusDisplayText,
  getStatusColor,
} from "@/services/matchStatusService";

interface MatchStatusDisplayProps {
  scheduledDate: string | null;
  status: string;
}

const MatchStatusDisplay: React.FC<MatchStatusDisplayProps> = ({
  scheduledDate,
  status,
}) => {
  const actualStatus = getMatchStatusBasedOnDate(scheduledDate, status);
  const statusColor = getStatusColor(actualStatus);
  const statusText = getStatusDisplayText(actualStatus);
  const timeDisplay = formatTime(scheduledDate);

  return (
    <VStack className="items-center px-4 flex-shrink-0 w-[50%]">
      <View className={`px-3 py-1 rounded-full ${statusColor} mb-2`}>
        <Text className="text-white text-[9px] font-urbanistBold uppercase">
          {statusText}
        </Text>
      </View>

      <VStack className="space-y-1 items-center">
        <Text className="text-gray-600 text-lg font-urbanistBold text-center">
          {formatDate(scheduledDate)}
        </Text>
        {timeDisplay && (
          <Text className="text-gray-600 text-sm font-urbanistSemiBold text-center">
            {timeDisplay}
          </Text>
        )}
      </VStack>
    </VStack>
  );
};

export default memo(MatchStatusDisplay);
